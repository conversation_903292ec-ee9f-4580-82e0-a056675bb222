import { Hono } from 'hono'
import type { Env } from '@/server/types/hono-env.types'
import {
	getLiveAuctions,
	getTrendingCollections,
	getTrendingCreators,
	getTrendingNFTs,
	type TimeFilter,
} from './helper'

const homeRoute = new Hono<Env>()

/**
 * Get live auction NFTs
 * Returns a maximum of 4 NFTs that are in active auctions
 */
homeRoute.get('live-auction-list', async (c) => {
	try {
		// Get live auctions with a limit of 4
		const auctions = await getLiveAuctions(4)
		return c.json(auctions)
	} catch (error) {
		console.error('Error in live-auction-list endpoint:', error)
		return c.json({ error: 'Failed to fetch live auctions' }, 500)
	}
})

/**
 * Get trending NFTs based on activity
 * Accepts an optional 'day' query parameter (1, 7, 30, 365)
 * Returns a maximum of 4 trending NFTs
 */
homeRoute.get('trending-nft-list', async (c) => {
	try {
		// Get day filter from query params (default to 1)
		const day = c.req.query('day')

		// Map numeric day values to TimeFilter
		let timeFilter: TimeFilter
		switch (day) {
			case '1':
				timeFilter = '1D'
				break
			case '7':
				timeFilter = '7D'
				break
			case '30':
				timeFilter = '30D'
				break
			case '365':
				// Use the 365D filter for 1 year
				timeFilter = '365D'
				break
			default:
				// Default to 1 day if no valid day parameter is provided
				timeFilter = '1D'
		}

		// Get trending NFTs with the specified time filter
		const trendingNFTs = await getTrendingNFTs(timeFilter, 4)
		return c.json(trendingNFTs)
	} catch (error) {
		console.error('Error in trending-nft-list endpoint:', error)
		return c.json({ error: 'Failed to fetch trending NFTs' }, 500)
	}
})

/**
 * Get trending collections
 * Accepts optional query parameters: timeFilter (1D, 7D, 30D, 365D) and limit
 * Returns trending collections with NFT images for homepage display
 */
homeRoute.get('trending-collection-list', async (c) => {
	try {
		// Get timeFilter and limit from query params
		const timeFilterParam = c.req.query('timeFilter') as TimeFilter | undefined
		const limitParam = c.req.query('limit')

		// Set defaults
		const timeFilter = timeFilterParam || '30D' // Default to 30 days for homepage
		const limit = limitParam ? Number.parseInt(limitParam, 10) : 6 // Default to 6 collections

		// Get trending collections
		const trendingCollections = await getTrendingCollections(timeFilter, limit)
		return c.json(trendingCollections)
	} catch (error) {
		console.error('Error in trending-collection-list endpoint:', error)
		return c.json({ error: 'Failed to fetch trending collections' }, 500)
	}
})

/**
 * Get trending creators based on NFT activity
 * Returns a maximum of 4 trending creators
 */
homeRoute.get('trending-creator-list', async (c) => {
	try {
		// Get trending creators
		const trendingCreators = await getTrendingCreators(4)
		return c.json(trendingCreators)
	} catch (error) {
		console.error('Error in trending-creator-list endpoint:', error)
		return c.json({ error: 'Failed to fetch trending creators' }, 500)
	}
})

export default homeRoute
