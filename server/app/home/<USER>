import type { Prisma } from 'prisma-client/edge'
import { prisma } from '@/server/lib/prismaClient'

export type TimeFilter = '1H' | '1D' | '7D' | '30D' | '365D'

// Define the type for NFT with listing data
export type NFTWithListing = {
	id: string
	name: string
	publicKey: string
	description?: string
	imageUrl: string
	lastBiddingPrice?: number
	totalLikes: number
	owner: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	creator?: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	collection?: {
		id: string
		name: string
		logoUrl: string
		publicKey: string
	}
	listing?: {
		id: string
		price: number
		currency: string
		status: string
		listingType: string
		startTime: Date
		endTime: Date | null
		isAuction: boolean
		isFixedPrice: boolean
		isActive: boolean
	}
}

// Define the type for creator with activity count
export type CreatorWithActivity = {
	id: string
	username: string
	imageUrl?: string
	publicKey: string
	activityCount: number
	nfts: Array<{
		id: string
		name: string
		imageUrl: string
		publicKey: string
	}>
}

/**
 * Get time range from filter
 * @param timeFilter Time filter string
 * @returns Date object representing the start of the time range
 */
export function getTimeRangeFromFilter(timeFilter?: TimeFilter): Date | null {
	if (!timeFilter) return null

	const now = new Date()

	switch (timeFilter) {
		case '1H':
			return new Date(now.getTime() - 60 * 60 * 1000) // 1 hour ago
		case '1D':
			return new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
		case '7D':
			return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
		case '30D':
			return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
		case '365D':
			return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) // 365 days ago (1 year)
		default:
			return null
	}
}

/**
 * Get live auction NFTs
 * @param limit Maximum number of NFTs to return
 * @returns Array of NFTs with active auctions
 */
export async function getLiveAuctions(limit = 4): Promise<NFTWithListing[]> {
	try {
		const now = new Date()

		// Find active auction listings that haven't ended yet
		const listings = await prisma.listing.findMany({
			where: {
				status: 'ACTIVE',
				listingType: 'AUCTION',
				endTime: {
					gt: now, // End time is in the future
				},
			},
			include: {
				nft: {
					include: {
						owner: true,
						collection: true,
					},
				},
			},
			orderBy: [
				{ endTime: 'asc' }, // Ending soonest first
			],
			take: limit,
		})

		// Transform to the expected format
		return listings.map((listing) => ({
			id: listing.nft.id,
			name: listing.nft.name,
			publicKey: listing.nft.publicKey,
			description: listing.nft.description || undefined,
			imageUrl: listing.nft.imageUrl,
			lastBiddingPrice: listing.nft.lastBiddingPrice || 0,
			totalLikes: listing.nft.totalLikes,
			owner: {
				id: listing.nft?.owner?.id,
				username: listing?.nft?.owner?.username,
				imageUrl: listing?.nft?.owner?.imageUrl || undefined,
				publicKey: listing?.nft?.owner?.publicKey,
			},
			collection: listing.nft.collection
				? {
						id: listing.nft.collection.id,
						name: listing.nft.collection.name,
						logoUrl: listing.nft.collection.logoUrl,
						publicKey: listing.nft.collection.publicKey,
					}
				: undefined,
			listing: {
				id: listing.id,
				price: listing.price,
				currency: listing.currency,
				status: listing.status,
				listingType: listing.listingType,
				startTime: listing.startTime,
				endTime: listing.endTime || null,
				isAuction: listing.listingType === 'AUCTION',
				isFixedPrice: listing.listingType === 'FIXED_PRICE',
				isActive: listing.status === 'ACTIVE',
			},
		}))
	} catch (error) {
		console.error('Error fetching live auctions:', error)
		return []
	}
}

/**
 * Get trending NFTs based on activity
 * @param timeFilter Time filter to apply
 * @param limit Maximum number of NFTs to return
 * @returns Array of trending NFTs
 */
export async function getTrendingNFTs(
	timeFilter: TimeFilter = '1D',
	limit = 4,
): Promise<NFTWithListing[]> {
	try {
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Get NFTs with the most activity in the given time range
		const nftsWithActivity = await prisma.activityLog.groupBy({
			by: ['nftId'],
			where: {
				nftId: { not: null },
				...(timeRange && { createdAt: { gte: timeRange } }),
			},
			_count: {
				id: true,
			},
			orderBy: {
				_count: {
					id: 'desc',
				},
			},
			take: limit,
		})

		// Extract NFT IDs
		const nftIds = nftsWithActivity
			.filter((item) => item.nftId !== null)
			.map((item) => item.nftId as string)

		if (nftIds.length === 0) {
			// Fallback to most recent NFTs if no activity
			const recentNFTs = await prisma.nFT.findMany({
				include: {
					owner: true,
					collection: true,
					listings: {
						where: { status: 'ACTIVE' },
						orderBy: { createdAt: 'desc' },
						take: 1,
					},
				},
				orderBy: { createdAt: 'desc' },
				take: limit,
			})

			return recentNFTs.map((nft) => ({
				id: nft.id,
				name: nft.name,
				publicKey: nft.publicKey,
				description: nft.description || undefined,
				imageUrl: nft.imageUrl,
				lastBiddingPrice: nft.lastBiddingPrice || 0,
				totalLikes: nft.totalLikes,
				owner: {
					id: nft.owner.id,
					username: nft.owner.username,
					imageUrl: nft.owner.imageUrl || undefined,
					publicKey: nft.owner.publicKey,
				},
				collection: nft.collection
					? {
							id: nft.collection.id,
							name: nft.collection.name,
							logoUrl: nft.collection.logoUrl,
							publicKey: nft.collection.publicKey,
						}
					: undefined,
				listing: nft.listings[0]
					? {
							id: nft.listings[0].id,
							price: nft.listings[0].price,
							currency: nft.listings[0].currency,
							status: nft.listings[0].status,
							listingType: nft.listings[0].listingType,
							startTime: nft.listings[0].startTime,
							endTime: nft.listings[0].endTime || null,
							isAuction: nft.listings[0].listingType === 'AUCTION',
							isFixedPrice: nft.listings[0].listingType === 'FIXED_PRICE',
							isActive: nft.listings[0].status === 'ACTIVE',
						}
					: undefined,
			}))
		}

		// Fetch the actual NFTs with their related data
		const nfts = await prisma.nFT.findMany({
			where: {
				id: { in: nftIds },
			},
			include: {
				owner: true,
				collection: true,
				listings: {
					where: { status: 'ACTIVE' },
					orderBy: { createdAt: 'desc' },
					take: 1,
				},
			},
		})

		// Transform to the expected format
		return nfts.map((nft) => ({
			id: nft.id,
			name: nft.name,
			publicKey: nft.publicKey,
			description: nft.description || undefined,
			imageUrl: nft.imageUrl,
			lastBiddingPrice: nft.lastBiddingPrice || 0,
			totalLikes: nft.totalLikes,
			owner: {
				id: nft.owner.id,
				username: nft.owner.username,
				imageUrl: nft.owner.imageUrl || undefined,
				publicKey: nft.owner.publicKey,
			},
			collection: nft.collection
				? {
						id: nft.collection.id,
						name: nft.collection.name,
						logoUrl: nft.collection.logoUrl,
						publicKey: nft.collection.publicKey,
					}
				: undefined,
			listing: nft.listings[0]
				? {
						id: nft.listings[0].id,
						price: nft.listings[0].price,
						currency: nft.listings[0].currency,
						status: nft.listings[0].status,
						listingType: nft.listings[0].listingType,
						startTime: nft.listings[0].startTime,
						endTime: nft.listings[0].endTime || null,
						isAuction: nft.listings[0].listingType === 'AUCTION',
						isFixedPrice: nft.listings[0].listingType === 'FIXED_PRICE',
						isActive: nft.listings[0].status === 'ACTIVE',
					}
				: undefined,
		}))
	} catch (error) {
		console.error('Error fetching trending NFTs:', error)
		return []
	}
}

/**
 * Get trending creators based on NFT activity
 * @param limit Maximum number of creators to return
 * @returns Array of trending creators with their NFTs
 */
export async function getTrendingCreators(
	limit = 4,
): Promise<CreatorWithActivity[]> {
	try {
		// Get NFTs with the most activity
		const trendingNFTs = await getTrendingNFTs('7D', 20)

		// Extract creator IDs from trending NFTs
		const creatorMap = new Map<
			string,
			{
				creator: {
					id: string
					username: string
					imageUrl?: string
					publicKey: string
				}
				nfts: Array<{
					id: string
					name: string
					imageUrl: string
					publicKey: string
				}>
				activityCount: number
			}
		>()

		// Get creator details for each NFT
		for (const nft of trendingNFTs) {
			// Skip if no creator
			if (!nft.owner) continue

			// Get creator details
			const creator = await prisma.user.findUnique({
				where: { id: nft.owner.id },
				include: {
					createdNFTs: {
						take: 4,
						orderBy: { createdAt: 'desc' },
					},
				},
			})

			if (!creator) continue

			// Count activities for this creator's NFTs
			const timeRange = getTimeRangeFromFilter('7D')
			const activityCount = await prisma.activityLog.count({
				where: {
					nft: {
						creatorId: creator.id,
					},
					...(timeRange && { createdAt: { gte: timeRange } }),
				},
			})

			// Add or update creator in the map
			if (creatorMap.has(creator.id)) {
				const existing = creatorMap.get(creator.id)
				if (existing) {
					existing.activityCount += activityCount
				}
			} else {
				creatorMap.set(creator.id, {
					creator: {
						id: creator.id,
						username: creator.username,
						imageUrl: creator.imageUrl || undefined,
						publicKey: creator.publicKey,
					},
					nfts: creator.createdNFTs.map((nft) => ({
						id: nft.id,
						name: nft.name,
						imageUrl: nft.imageUrl,
						publicKey: nft.publicKey,
					})),
					activityCount,
				})
			}
		}

		// Convert map to array and sort by activity count
		const creators = Array.from(creatorMap.values())
			.sort((a, b) => b.activityCount - a.activityCount)
			.slice(0, limit)

		// Transform to the expected format
		return creators.map((item) => ({
			id: item.creator.id,
			username: item.creator.username,
			imageUrl: item.creator.imageUrl,
			publicKey: item.creator.publicKey,
			activityCount: item.activityCount,
			nfts: item.nfts,
		}))
	} catch (error) {
		console.error('Error fetching trending creators:', error)
		return []
	}
}

/**
 * Get trending collections for homepage
 * @param timeFilter Time filter to apply
 * @param limit Maximum number of collections to return
 * @returns Array of trending collections with NFT images
 */
export async function getTrendingCollections(
	timeFilter: TimeFilter = '1D',
	limit = 5,
): Promise<CollectionWithImages[]> {
	try {
		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for collections
		const where: Prisma.CollectionWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
		}

		// Fetch collections with pagination and include owner and NFTs
		const collections = await prisma.collection.findMany({
			where,
			include: {
				owner: true,
				nfts: {
					take: 3, // Get 3 NFTs for display
					orderBy: { createdAt: 'desc' },
				},
			},
			take: limit,
			orderBy: [
				// Use fields that definitely exist in the DB schema
				{ createdAt: 'desc' },
			],
		})

		// Map to the expected Collection type with three NFT images
		return collections.map((collection) => {
			const lowestBiddingNFT = collection.nfts[0]

			return {
				symbol: collection.symbol,
				name: collection.name,
				publicKey: collection.publicKey,
				description: collection.description,
				logoUrl: collection.logoUrl,
				bannerUrl: collection.bannerUrl,
				metadataUri: collection.metadataUri,
				creator: {
					id: collection.owner.id,
					username: collection.owner.username,
					imageUrl: collection.owner.imageUrl || '',
					publicKey: collection.owner.publicKey,
				},
				// Required by Collection type
				lowestBiddingPrice: lowestBiddingNFT?.lastBiddingPrice ?? 0,
				// Additional field for images display
				nftImages: collection.nfts
					.map((nft) => ({
						img: nft.imageUrl || collection.logoUrl,
					}))
					.slice(0, 3),
			}
		})
	} catch (error) {
		console.error('Error fetching trending collections:', error)
		return []
	}
}

// Define the CollectionWithImages type that extends Collection
export type CollectionWithImages = Collection & {
	nftImages?: Array<{ img: string }>
}
