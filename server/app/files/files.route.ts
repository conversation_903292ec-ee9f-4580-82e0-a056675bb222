import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { Hono } from 'hono'
import { z } from 'zod'
import { v4 as uuidv4 } from 'uuid'
import { tryCatch } from '@/lib/try-catch'
import { s3Client } from '@/server/lib/s3Client'
import type { Env } from '../../types/hono-env.types'

const filesRoute = new Hono<Env>()

// Validation schema for presigned URL request
const presignedUrlSchema = z.object({
	fileName: z.string().min(1, 'File name is required'),
	fileType: z.string().min(1, 'File type is required'),
})

/**
 * POST /api/files/presigned-url
 * Generate a presigned URL for S3/R2 file upload
 */
filesRoute.post('/presigned-url', async (c) => {
	try {
		const json = await c.req.json()
		
		const parsed = presignedUrlSchema.safeParse(json)
		if (!parsed.success) {
			return c.json(
				{
					success: false,
					message: 'Invalid request body',
					errors: parsed.error.errors,
				},
				400,
			)
		}

		const { fileName, fileType } = parsed.data

		const bucketName = process.env.R2_BUCKET_NAME || 'nft-marketplace'
		const uniqueFileName = `uploads/${uuidv4()}-${fileName}`
		
		const command = new PutObjectCommand({
			Bucket: bucketName,
			Key: uniqueFileName,
			ContentType: fileType,
			ACL: 'public-read',
		})

		const { data: url, error: urlError } = await tryCatch(
			getSignedUrl(s3Client, command, { expiresIn: 3600 }),
		)

		if (urlError || !url) {
			console.error('Error generating presigned URL:', urlError)
			return c.json(
				{
					success: false,
					message: 'Failed to generate upload URL',
				},
				500,
			)
		}

		const fileUrl = `https://${process.env.R2_PUBLIC_DOMAIN}/${uniqueFileName}`

		return c.json(
			{
				success: true,
				url,
				fileUrl,
			},
			200,
		)
	} catch (error) {
		console.error('Unexpected error in presigned URL generation:', error)
		return c.json(
			{
				success: false,
				message: 'An unknown error occurred',
			},
			500,
		)
	}
})

export { filesRoute }
