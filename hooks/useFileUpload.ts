import { useMutation } from '@tanstack/react-query'
import { useState } from 'react'

type PresignedUrlRequest = {
	fileName: string
	fileType: string
}

type PresignedUrlResponse = {
	success: boolean
	url?: string
	fileUrl?: string
	message?: string
	errors?: Array<{ message: string }>
}

type FileUploadResult = {
	success: boolean
	message: string
	url: string
}

/**
 * Get a presigned URL for file upload from the REST API
 */
const getPresignedUrl = async (request: PresignedUrlRequest): Promise<PresignedUrlResponse> => {
	const res = await fetch('/api/files/presigned-url', {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(request),
	})

	if (!res.ok) {
		const errorData = await res.json()
		throw new Error(errorData.message || 'Failed to get presigned URL')
	}

	return res.json() as Promise<PresignedUrlResponse>
}

/**
 * Upload a file using the presigned URL
 */
const uploadFileToS3 = async (file: File, presignedUrl: string): Promise<void> => {
	const abortController = new AbortController()

	// Abort the request if it takes longer than 60 seconds
	setTimeout(() => {
		abortController.abort()
	}, 60000)

	const response = await fetch(presignedUrl, {
		method: 'PUT',
		headers: {
			'Content-Type': file.type,
		},
		body: file,
		signal: abortController.signal,
	})

	if (!response.ok) {
		throw new Error(`HTTP Error: ${response.status}`)
	}
}

/**
 * Complete file upload process: get presigned URL and upload file
 */
const uploadFile = async (file: File): Promise<FileUploadResult> => {
	try {
		// Get presigned URL from server
		const presignedUrlResult = await getPresignedUrl({
			fileName: file.name,
			fileType: file.type,
		})

		if (!presignedUrlResult.success || !presignedUrlResult.url) {
			throw new Error(presignedUrlResult.message || 'Failed to get upload URL')
		}

		// Upload file to S3/R2
		await uploadFileToS3(file, presignedUrlResult.url)

		// Return the final URL
		return {
			success: true,
			message: 'File uploaded successfully',
			url: presignedUrlResult.fileUrl || '',
		}
	} catch (error) {
		return {
			success: false,
			message: error instanceof Error ? error.message : 'Error uploading file',
			url: '',
		}
	}
}

export function useFileUpload() {
	const [error, setError] = useState<string | null>(null)

	const uploadMutation = useMutation<FileUploadResult, Error, File>({
		mutationFn: uploadFile,
		onError: (err: Error) => {
			setError(err.message)
		},
		onSuccess: () => {
			setError(null)
		},
	})

	const presignedUrlMutation = useMutation<PresignedUrlResponse, Error, PresignedUrlRequest>({
		mutationFn: getPresignedUrl,
		onError: (err: Error) => {
			setError(err.message)
		},
		onSuccess: () => {
			setError(null)
		},
	})

	/**
	 * Upload a file to S3/R2 using presigned URL
	 * @param file - The file to upload
	 * @returns Promise<FileUploadResult> - The upload result with final URL
	 */
	const upload = async (file: File): Promise<FileUploadResult> => {
		return await uploadMutation.mutateAsync(file)
	}

	/**
	 * Get a presigned URL for file upload
	 * @param fileName - Name of the file
	 * @param fileType - MIME type of the file
	 * @returns Promise<PresignedUrlResponse> - The presigned URL response
	 */
	const getPresignedUrlOnly = async (fileName: string, fileType: string): Promise<PresignedUrlResponse> => {
		return await presignedUrlMutation.mutateAsync({ fileName, fileType })
	}

	return {
		upload,
		getPresignedUrlOnly,
		isUploading: uploadMutation.status === 'pending',
		isGettingPresignedUrl: presignedUrlMutation.status === 'pending',
		error,
		// Expose mutations for direct access if needed
		uploadMutation,
		presignedUrlMutation,
	}
}

// Export the uploadFile function for backward compatibility
export { uploadFile }
