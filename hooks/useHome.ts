import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { ofetch } from 'ofetch'
import { tryCatch } from '@/lib/try-catch'

// Define types for the API responses
export type NFTWithListing = {
	id: string
	name: string
	publicKey: string
	description?: string
	imageUrl: string
	lastBiddingPrice?: number
	totalLikes: number
	totalViews: number
	owner: {
		id: string
		username: string
		imageUrl?: string
		publicKey: string
	}
	collection?: {
		id: string
		name: string
		logoUrl: string
		publicKey: string
	}
	listing?: {
		id: string
		price: number
		currency: string
		status: string
		listingType: string
		startTime: Date
		endTime: Date | null
		isAuction: boolean
		isFixedPrice: boolean
		isActive: boolean
	}
}

export type CreatorWithActivity = {
	id: string
	username: string
	imageUrl?: string
	publicKey: string
	activityCount: number
	nfts: Array<{
		id: string
		name: string
		imageUrl: string
		publicKey: string
	}>
}

export type CollectionWithImages = {
	symbol: string
	name: string
	publicKey: string
	description: string
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	creator: {
		id: string
		username: string
		imageUrl: string
		publicKey: string
	}
	lowestBiddingPrice: number
	nftImages?: Array<{ img: string }>
}

const fetchLiveAuctionList = async (): Promise<NFTWithListing[]> => {
	const { data, error } = await tryCatch(
		ofetch<NFTWithListing[]>('/api/home/<USER>', {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching live auction list: ${error}`)
	}

	return data
}

const fetchTrendingNFTList = async (
	day: 1 | 7 | 30 | 365 = 1,
): Promise<NFTWithListing[]> => {
	// Convert numeric day to string day parameter
	const dayParam = day.toString()

	const { data, error } = await tryCatch(
		ofetch<NFTWithListing[]>(`/api/home/<USER>
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching trending NFT list: ${error}`)
	}

	return data
}

const fetchTrendingCollectionList = async (
	timeFilter?: string,
	limit?: number,
): Promise<CollectionWithImages[]> => {
	// Build query parameters
	const params = new URLSearchParams()
	if (timeFilter) params.append('timeFilter', timeFilter)
	if (limit) params.append('limit', limit.toString())

	const url = `/api/home/<USER>''}`

	const { data, error } = await tryCatch(
		ofetch<CollectionWithImages[]>(url, {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching trending collection list: ${error}`)
	}

	return data
}

const fetchTrendingCreatorList = async (): Promise<CreatorWithActivity[]> => {
	const { data, error } = await tryCatch(
		ofetch<CreatorWithActivity[]>('/api/home/<USER>', {
			method: 'GET',
		}),
	)

	if (error) {
		throw new Error(`Error fetching trending creator list: ${error}`)
	}

	return data
}

export const useLiveAuction = (options?: UseQueryOptions<NFTWithListing[]>) => {
	return useQuery<NFTWithListing[]>({
		queryKey: ['live_auction_list'],
		queryFn: () => fetchLiveAuctionList(),
		refetchInterval: 60000,
		refetchOnMount: true,
		...options,
	})
}

export const useTrendingNFTList = (
	day: 1 | 7 | 30 | 365 = 1,
	options?: UseQueryOptions<NFTWithListing[]>,
) => {
	return useQuery<NFTWithListing[]>({
		queryKey: ['trending_nft_list', day],
		queryFn: () => fetchTrendingNFTList(day),
		refetchInterval: 60000,
		refetchOnMount: true,
		...options,
	})
}

export const useTrendingCollectionList = (
	timeFilter?: string,
	limit?: number,
	options?: UseQueryOptions<CollectionWithImages[]>,
) => {
	return useQuery<CollectionWithImages[]>({
		queryKey: ['trending_collection_list', timeFilter, limit],
		queryFn: () => fetchTrendingCollectionList(timeFilter, limit),
		refetchInterval: 60000,
		refetchOnMount: true,
		staleTime: 5 * 60 * 1000, // 5 minutes - trending data doesn't change frequently
		gcTime: 10 * 60 * 1000, // 10 minutes
		...options,
	})
}

export const useTrendingCreatorList = (
	options?: UseQueryOptions<CreatorWithActivity[]>,
) => {
	return useQuery<CreatorWithActivity[]>({
		queryKey: ['trending_creator_list'],
		queryFn: () => fetchTrendingCreatorList(),
		refetchInterval: 60000,
		refetchOnMount: true,
		...options,
	})
}
