import { type UseQueryOptions, useQuery } from '@tanstack/react-query'
import { tryCatch } from '@/lib/try-catch'

/**
 * Type definition for user collection data from /api/users/collections
 * Matches the response from the user collections endpoint
 */
export type UserCollection = {
	id: string
	publicKey: string
	name: string
	logoUrl: string | null
	royaltyBasisPoints: number
}

/**
 * Fetch the current authenticated user's collections from the user API
 * Uses the /api/users/collections endpoint which requires authentication
 */
export const fetchUserCollections = async (): Promise<UserCollection[]> => {
	const accessToken = localStorage.getItem('accessToken')
	
	if (!accessToken) {
		throw new Error('No access token available')
	}

	const { data, error } = await tryCatch(
		fetch('/api/users/collections', {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
			},
		}),
	)

	if (error) {
		console.error('Error fetching user collections:', error)
		throw new Error(`Error fetching user collections: ${error}`)
	}

	if (!data.ok) {
		const errorData = await data.json()
		throw new Error(errorData.message || 'Failed to fetch user collections')
	}

	const collections = await data.json()

	if (!Array.isArray(collections)) {
		throw new Error('Invalid collections data returned from API')
	}

	return collections
}

/**
 * Hook to fetch and manage the current authenticated user's collections
 * Uses the /api/users/collections endpoint with proper authentication
 *
 * @param options - Optional React Query options to customize the query behavior
 * @returns React Query result with the user's collections
 *
 * @example
 * ```tsx
 * const { data: collections, isLoading, error } = useUserCollections();
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage error={error} />;
 * if (!collections || collections.length === 0) return <NoCollections />;
 *
 * return (
 *   <CollectionDropdown>
 *     {collections.map(collection => (
 *       <option key={collection.id} value={collection.publicKey}>
 *         {collection.name}
 *       </option>
 *     ))}
 *   </CollectionDropdown>
 * );
 * ```
 */
export const useUserCollections = (options?: UseQueryOptions<UserCollection[]>) => {
	return useQuery<UserCollection[]>({
		queryKey: ['userCollections'],
		queryFn: fetchUserCollections,
		retry: (failureCount, error) => {
			// Don't retry on authentication errors
			if (error.message.includes('Unauthorized') || error.message.includes('No access token')) {
				return false
			}
			// Retry up to 3 times for other errors
			return failureCount < 3
		},
		staleTime: 5 * 60 * 1000, // 5 minutes - collections don't change frequently
		gcTime: 10 * 60 * 1000, // 10 minutes
		...options,
	})
}
