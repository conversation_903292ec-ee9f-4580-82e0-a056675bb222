import { fetchAssetsByOwner } from '@metaplex-foundation/mpl-core'
import { publicKey } from '@metaplex-foundation/umi'
import type { Prisma } from 'prisma-client/edge'
import { enhanceAssetWithMetadata } from '@/lib/nft/asset-utils'
import { prisma } from '@/server/lib/prismaClient'
import { getServerUmi } from '@/server/lib/umiClient'
import type { Collection } from '@/types/enhanced-assets'

export type CreatedNFT = Prisma.NFTGetPayload<{
	select: {
		publicKey: true
		imageUrl: true
		name: true
		description: true
		lastBiddingPrice: true
		owner: {
			select: {
				userName: true
			}
		}
	}
}>

type UserParams = {
	userName?: string
	page?: number
	limit?: number
}

/**
 * MIGRATED FUNCTION: onGetNFTsByUser → GET /api/users/nfts (see hooks/useProfile.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useUserNfts hook.
 */
export async function onGetNFTsByUser({ userName }: UserParams) {
	try {
		const umi = getServerUmi()
		const user = await prisma.user.findFirst({
			where: {
				username: userName?.toLowerCase(),
			},
		})
		if (!user) {
			console.error('User not found')
			return []
		}
		const assets = await fetchAssetsByOwner(umi, publicKey(user.publicKey))
		if (!assets) {
			console.error('No assets found for the user')
			return []
		}

		const enhancedAssets = await Promise.all(
			assets.map(async (asset) => {
				const enhancedAsset = await enhanceAssetWithMetadata(asset)
				if (!enhancedAsset) {
					console.error('Failed to enhance asset with metadata')
					return null
				}
				return enhancedAsset
			}),
		).then((assets) => assets.filter((asset) => asset !== null))

		return enhancedAssets
	} catch (error) {
		console.error('Error fetching NFTs:', error)
		return []
	}
}

/**
 * MIGRATED FUNCTION: onGetUserCreatedNfts → GET /api/users/:username/created-nfts (see hooks/useProfile.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useUserCreatedNfts hook.
 */
export async function onGetUserCreatedNfts({
	userName,
	page = 1,
	limit = 20,
}: UserParams): Promise<{
	nfts: CreatedNFT[]
	pagination: {
		totalItems: number
		totalPages: number
		currentPage: number
		itemsPerPage: number
	}
}> {
	try {
		const user = await prisma.user.findFirst({
			where: {
				username: userName?.toLowerCase(),
			},
		})
		if (!user) {
			console.error('User not found')
			return {
				nfts: [],
				pagination: {
					totalItems: 0,
					totalPages: 0,
					currentPage: page,
					itemsPerPage: limit,
				},
			}
		}

		// Get total count for pagination
		const totalItems = await prisma.nFT.count({
			where: {
				creator: {
					publicKey: user.publicKey,
				},
			},
		})

		// Calculate total pages
		const totalPages = Math.ceil(totalItems / limit)

		// Get paginated NFTs
		const nfts = await prisma.nFT.findMany({
			where: {
				creator: {
					publicKey: user.publicKey,
				},
			},
			skip: (page - 1) * limit,
			take: limit,
		})

		if (!nfts) {
			console.error('No NFTs found for the user')
			return {
				nfts: [],
				pagination: {
					totalItems: 0,
					totalPages: 0,
					currentPage: page,
					itemsPerPage: limit,
				},
			}
		}

		return {
			nfts: nfts.map((nft) => ({
				...nft,
				owner: {
					userName: user.userName,
				},
			})) as CreatedNFT[],
			pagination: {
				totalItems,
				totalPages,
				currentPage: page,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching NFTs:', error)
		return {
			nfts: [],
			pagination: {
				totalItems: 0,
				totalPages: 0,
				currentPage: page,
				itemsPerPage: limit,
			},
		}
	}
}

/**
 * MIGRATED FUNCTION: onUserAssetCounts → GET /api/users/:username/asset-counts (see hooks/useProfile.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useUserAssetCounts hook.
 */
export async function onUserAssetCounts(
	userName: string,
): Promise<{ totalMintedNfts: number; totalNftCollections: number }> {
	const totalMintedNfts = await prisma.nFT.count({
		where: {
			creator: {
				username: userName.toLowerCase(),
			},
		},
	})
	const collectionsData = await onGetUserCollections({
		userName: userName.toLowerCase(),
	})
	return {
		totalMintedNfts,
		totalNftCollections: collectionsData.pagination.totalItems,
	}
}

// create a function to get the nft collection by user where the user owns any of the nfts in the collection
/**
 * MIGRATED FUNCTION: onGetUserCollections → GET /api/users/:username/collections (see hooks/useProfile.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useUserCollectionsByUsername hook.
 */
export async function onGetUserCollections({
	userName,
	page = 1,
	limit = 20,
}: UserParams): Promise<{
	collections: Collection[]
	pagination: {
		totalItems: number
		totalPages: number
		currentPage: number
		itemsPerPage: number
	}
}> {
	try {
		const user = await prisma.user.findFirst({
			where: {
				username: userName?.toLowerCase(),
			},
		})
		if (!user) {
			console.error('User not found')
			return {
				collections: [],
				pagination: {
					totalItems: 0,
					totalPages: 0,
					currentPage: page,
					itemsPerPage: limit,
				},
			}
		}

		// Get total count for pagination
		const totalItems = await prisma.collection.count({
			where: {
				owner: {
					id: user.id,
				},
			},
		})

		// Calculate total pages
		const totalPages = Math.ceil(totalItems / limit)

		const ownedCollections = await prisma.collection.findMany({
			where: {
				owner: {
					id: user.id,
				},
			},
			include: {
				owner: true,
				nfts: {
					orderBy: { lastBiddingPrice: 'asc' },
				},
			},
			orderBy: { createdAt: 'desc' },
			skip: (page - 1) * limit,
			take: limit,
		})

		if (!ownedCollections) {
			console.error('No collections found for the user')
			return {
				collections: [],
				pagination: {
					totalItems: 0,
					totalPages: 0,
					currentPage: page,
					itemsPerPage: limit,
				},
			}
		}

		const collections = ownedCollections.map((collection) => {
			const lowestBiddingNFT = collection.nfts[0]
			return {
				symbol: collection.symbol,
				name: collection.name,
				publicKey: collection.publicKey,
				description: collection.description,
				logoUrl: collection.logoUrl,
				bannerUrl: collection.bannerUrl,
				metadataUri: collection.metadataUri,
				owner: {
					id: collection.owner.id,
					username: collection.owner.username,
					imageUrl: collection.owner.imageUrl || '',
					publicKey: collection.owner.publicKey,
				},
				lowestBiddingPrice: lowestBiddingNFT?.lastBiddingPrice ?? 0,
			}
		})

		return {
			collections,
			pagination: {
				totalItems,
				totalPages,
				currentPage: page,
				itemsPerPage: limit,
			},
		}
	} catch (error) {
		console.error('Error fetching collections:', error)
		return {
			collections: [],
			pagination: {
				totalItems: 0,
				totalPages: 0,
				currentPage: page,
				itemsPerPage: limit,
			},
		}
	}
}

/**
 * MIGRATED FUNCTION: onCheckIfFollowing → GET /api/users/check-follow (see hooks/useFollowUser.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useFollowStatus hook.
 */
// Function to check if the current logged-in user is following another user
export async function onCheckIfFollowing({ userId }: { userId: string }) {
	try {
		// Get current user context (implement or use your auth mechanism here)
		const loggedInUser = await prisma.user.findFirst({
			where: {
				// Note: Replace this with the correct way to identify the current user in your app
				// This is a placeholder implementation
				id: 'current-user-id',
			},
		})

		if (!loggedInUser) {
			return { isFollowing: false }
		}

		// Check if the current user is following the specified user
		const follow = await prisma.follow.findUnique({
			where: {
				followerId_followingId: {
					followerId: loggedInUser.id,
					followingId: userId,
				},
			},
		})

		return { isFollowing: !!follow }
	} catch (error) {
		console.error('Error checking follow status:', error)
		return { isFollowing: false }
	}
}
