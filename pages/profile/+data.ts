import { isPublicKey } from '@metaplex-foundation/umi'
import type { Prisma } from 'prisma-client/edge'
import type { PageContextServer } from 'vike/types'
import { prisma } from '@/server/lib/prismaClient'
import type { EnhancedAsset } from '@/types/enhanced-assets'
// Asset counts are now fetched client-side using useUserAssetCounts hook

export type User = Prisma.UserGetPayload<{
	select: {
		id: true
		imageUrl: true
		publicKey: true
		bio: true
		bannerUrl: true
		username: true
		twitterId: true
		facebookId: true
		instagramId: true
		telegramId: true
		websiteId: true
	}
}> & {
	followingCount?: number
	followersCount?: number
}

export type Counts = {
	totalMintedNfts: number
	totalNftCollections: number
}

export default async function data(
	pageContext: PageContextServer,
): Promise<{ user: User; counts: Counts }> {
	// Get user identifier from page context route parameters
	const slug = pageContext.routeParams.slug
	if (!slug) {
		console.error('No user Address/Username provided in route parameters')
		throw new Error('No user Address/Username provided in route parameters')
	}

	// Check if the slug is a public key or username
	const isPublicKeyParam = isPublicKey(slug)

	// Build the query based on whether we have a public key or username
	const user = await prisma.user.findFirst({
		where: isPublicKeyParam
			? { publicKey: slug }
			: { username: slug.toLowerCase() },
	})

	if (!user) {
		console.error('User not found')
		throw new Error('User not found')
	}

	if (isPublicKeyParam) {
		pageContext.routeParams = {
			slug: user.username,
		}
	}

	// Asset counts are now fetched client-side using useUserAssetCounts hook
	// Return empty counts since they're fetched via REST API on the client
	const counts: Counts = {
		totalMintedNfts: 0,
		totalNftCollections: 0,
	}

	return {
		user,
		counts,
	}
}
