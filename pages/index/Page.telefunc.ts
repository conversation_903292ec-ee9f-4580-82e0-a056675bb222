import type { Prisma } from 'prisma-client/edge'
import { prisma } from '@/server/lib/prismaClient'
import type { Collection } from '@/types/enhanced-assets'

// Define types for filtering parameters
export type TimeFilter = '1H' | '1D' | '7D' | '30D'
export type HomeParams = {
	timeFilter?: TimeFilter
	limit?: number
}

// Define the CollectionWithImages type that extends Collection
export type CollectionWithImages = Collection & {
	nftImages?: Array<{ img: string }>
}

/**
 * MIGRATED FUNCTION: onGetTrendingCollections → GET /api/home/<USER>/useHome.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useTrendingCollectionList hook.
 */
export async function onGetTrendingCollections(
	params: HomeParams = {},
): Promise<CollectionWithImages[]> {
	try {
		const { timeFilter = '1D', limit = 5 } = params

		// Calculate time range based on filter
		const timeRange = getTimeRangeFromFilter(timeFilter)

		// Build where clause for collections
		const where: Prisma.CollectionWhereInput = {
			...(timeRange && { createdAt: { gte: timeRange } }),
		}

		// Fetch collections with pagination and include owner and NFTs
		const collections = await prisma.collection.findMany({
			where,
			include: {
				owner: true,
				nfts: {
					take: 3, // Get 3 NFTs for display
					orderBy: { createdAt: 'desc' },
				},
			},
			take: limit,
			orderBy: [
				// Use fields that definitely exist in the DB schema
				{ createdAt: 'desc' },
			],
		})

		// Map to the expected Collection type with three NFT images
		return collections.map((collection) => {
			const lowestBiddingNFT = collection.nfts[0]

			return {
				symbol: collection.symbol,
				name: collection.name,
				publicKey: collection.publicKey,
				description: collection.description,
				logoUrl: collection.logoUrl,
				bannerUrl: collection.bannerUrl,
				metadataUri: collection.metadataUri,
				creator: {
					id: collection.owner.id,
					username: collection.owner.username,
					imageUrl: collection.owner.imageUrl || '',
					publicKey: collection.owner.publicKey,
				},
				// Required by Collection type
				lowestBiddingPrice: lowestBiddingNFT?.lastBiddingPrice ?? 0,
				// Additional field for images display
				nftImages: collection.nfts
					.map((nft) => ({
						img: nft.imageUrl || collection.logoUrl,
					}))
					.slice(0, 3),
			}
		})
	} catch (error) {
		console.error('Error fetching trending collections:', error)
		return []
	}
}

/**
 * Helper function to convert time filter to Date object
 */
function getTimeRangeFromFilter(timeFilter?: TimeFilter): Date | null {
	if (!timeFilter) return null

	const now = new Date()

	switch (timeFilter) {
		case '1H':
			return new Date(now.getTime() - 60 * 60 * 1000) // 1 hour ago
		case '1D':
			return new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
		case '7D':
			return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
		case '30D':
			return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
		default:
			return null
	}
}
