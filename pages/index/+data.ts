// This data file is no longer used since the home page now uses
// the useTrendingCollectionList hook to fetch trending collections
// from the REST API endpoint /api/home/<USER>
// instead of server-side data fetching

export type HomeData = {
	trendingCollections: never[]
}

export default async function data(): Promise<HomeData | null> {
	// Return empty data since we now use client-side data fetching
	return {
		trendingCollections: [],
	}
}
