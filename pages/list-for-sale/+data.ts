// This data file is no longer used since the list-for-sale page now uses
// the useNFTBase hook to fetch NFT data from the REST API endpoint
// /api/nft/detail/:publicKey instead of server-side data fetching

export type ListForSaleData = {
	nft: null
}

export default async function data(): Promise<ListForSaleData | null> {
	// Return null since we now use client-side data fetching
	return {
		nft: null,
	}
}
