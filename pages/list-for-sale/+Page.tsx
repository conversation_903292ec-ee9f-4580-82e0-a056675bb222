import { useWallet } from '@solana/wallet-adapter-react'
import { Image } from '@unpic/react'
import { usePageContext } from 'vike-react/usePageContext'
import MyBreadcrumb from '@/components/MyBreadcrumb'
import { useNFTBase } from '@/hooks/useNFTDetails'
import { roundDown } from '@/lib/utils'
import ListForSaleForm from './ListForSaleForm'

const breadcrumbs = [
	{ id: 1, title: 'Home', url: '/' },
	{ id: 2, title: 'List For Sale' },
]

export default function Page() {
	const pageContext = usePageContext()
	const wallet = useWallet()
	const assetPublicKey = pageContext.routeParams.id

	// Use the REST API hook to fetch NFT data
	const { data: nftData, isLoading, error } = useNFTBase(assetPublicKey || '')
	const nft = nftData?.nft || null

	// If we don't have the NFT data, use a placeholder owner (current wallet)
	const ownerPublicKey =
		nft?.owner?.publicKey || wallet.publicKey?.toBase58() || ''

	// Show loading state
	if (isLoading) {
		return (
			<div className='containerPadding pt-4'>
				<MyBreadcrumb items={breadcrumbs} />
				<div className='mt-[80px]'>
					<h1 className='text-2xl lg:text-5xl font-semibold'>List for Sale</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
						Loading NFT details...
					</span>
				</div>
			</div>
		)
	}

	// Show error state
	if (error) {
		return (
			<div className='containerPadding pt-4'>
				<MyBreadcrumb items={breadcrumbs} />
				<div className='mt-[80px]'>
					<h1 className='text-2xl lg:text-5xl font-semibold'>List for Sale</h1>
					<span className='inline-block text-sm lg:text-xl font-light text-red-500 lg:mt-3'>
						Error loading NFT: {error.message}
					</span>
				</div>
			</div>
		)
	}

	return (
		<div className='containerPadding pt-4'>
			<MyBreadcrumb items={breadcrumbs} />
			<div className='mt-[80px]'>
				<h1 className='text-2xl lg:text-5xl font-semibold'>List for Sale</h1>
				<span className='inline-block text-sm lg:text-xl font-light text-[#7E7E7E] lg:mt-3'>
					Fill in the details to mint your unique NFT to the blockchain
				</span>
			</div>

			<div className='flex flex-col lg:grid lg:grid-cols-[1fr_clamp(0px,45%,530px)] gap-x-5 lg:mt-[60px] mt-6'>
				<div className='order-2 lg:order-none mt-7 lg:mt-0'>
					<ListForSaleForm
						royalty={(nft?.royaltyBasisPoints ?? 0) / 100}
						assetPublicKey={assetPublicKey || ''}
						ownerPublicKey={ownerPublicKey}
						collectionPublicKey={nft?.collection?.publicKey}
					/>
				</div>

				{/* --------------nft-card------------ */}
				<div>
					<div className='rounded-[20px] overflow-hidden order-1 lg:order-none max-w-[532px] mx-auto'>
						<Image
							width={530}
							height={530}
							layout='fixed'
							className='object-cover aspect-square'
							src={nft?.logoUrl || '/assets/temp/trending-nft2.png'}
							alt={nft?.name || 'NFT Image'}
						/>
						<div className='bg-[#2D2D2D] py-4 px-5'>
							<span className='text-sm lg:text-base font-semibold'>
								{nft?.name || 'Loading NFT...'}
							</span>
							<div className='flex items-center justify-between border-t-[0.5px] gradientBorder py-3 mt-3'>
								<span className='text-xs font-light'>Owner</span>
								<div className='flex gap-x-1'>
									<span className='text-xs font-bold'>
										{nft?.owner?.username || 'Unknown'}
									</span>
								</div>
							</div>
							{nft?.biddingPrice ? (
								<div className='flex items-center justify-between border-t-[0.5px] gradientBorder py-3 mt-3'>
									<span className='text-xs font-light'>Last Price</span>
									<div className='flex gap-x-1'>
										<span className='text-xs font-bold'>
											{roundDown(nft.biddingPrice, 2)} SOL
										</span>
									</div>
								</div>
							) : null}
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
