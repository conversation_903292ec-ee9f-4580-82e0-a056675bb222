import type { Prisma } from 'prisma-client/edge'

export type Collection = {
	id: string
	publicKey: string
	name: string
	royaltyBasisPoints: number
	logoUrl: string
}

export type CollectionList = Prisma.CollectionGetPayload<{
	select: {
		id: true
		publicKey: true
		name: true
		logoUrl: true
		royaltyBasisPoints: true
	}
}>

// This data client is no longer used since we migrated to useUserCollections hook
// The mint page now uses the REST API endpoint /api/users/collections
export default async function data(): Promise<CollectionList[]> {
	// Return empty array since this is no longer used
	return []
}
