import type { Prisma } from 'prisma-client/edge'
import { fetchMe } from '@/hooks/useMe'
import { tryCatch } from '@/lib/try-catch'

export type PublicUser = Prisma.UserGetPayload<{
	select: {
		id: true
		publicKey: true
		userName: true
		email: true
		bio: true
		imageUrl: true
		bannerUrl: true
		instagramId: true
		telegramId: true
		twitterId: true
		websiteId: true
		facebookId: true
	}
}>

export default async function data(): Promise<PublicUser | null> {
	const { data: user, error: noUserError } = await tryCatch(fetchMe())

	if (noUserError) {
		return null
	}
	return user
}
