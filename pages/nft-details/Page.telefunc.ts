import { B<PERSON><PERSON> } from 'node:buffer'
import { fetchAsset } from '@metaplex-foundation/mpl-core'
import { PublicKey } from '@solana/web3.js'
import type { Prisma } from 'prisma-client/edge'
import { FUSION_MARKETPLACE_PROGRAM_ID } from '@/lib/generated'
import {
	enhanceAssetWithMetadata,
	fetchEnhancedCollection,
	fetchMetadata,
} from '@/lib/nft/asset-utils'
import { prisma } from '@/server/lib/prismaClient'
import { getServerUmi } from '@/server/lib/umiClient'
import type { NFT as BaseNFT } from '@/types/enhanced-assets'

// Extended NFT type with additional properties needed for the details page
export type NFT = BaseNFT & {
	collectionId?: string
	royaltyBasisPoints?: number
	updatedAt?: string
	metadataUrl?: string
	isLocked?: boolean
	attributes?: Array<{
		id: string
		traitType: string
		value: string
	}>
	collection?: {
		name: string
		description?: string
		logoUrl?: string
		bannerUrl?: string
		owner?: {
			id: string
			username: string
			imageUrl?: string
			publicKey: string
		}
		publicKey: string
	}
}

// Constants for marketplace program
const MARKETPLACE_PROGRAM_ID = FUSION_MARKETPLACE_PROGRAM_ID
const LISTING_SEED = 'listing'
/**
 * MIGRATED FUNCTION: onGetNFT → GET /api/nft/detail/:publicKey (see hooks/useNFTDetails.ts)
 * This function has been migrated to REST API endpoint and is no longer used.
 * The functionality is now available through the useNFTBase hook.
 */
export async function onGetNFT(publicKey: string): Promise<NFT | null> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to onGetNFT')
			return null
		}

		// Define the NFT type with proper includes
		type NFTWithIncludes = Prisma.NFTGetPayload<{
			include: {
				owner: true
				collection: {
					include: {
						owner: true
					}
				}
			}
		}>

		// Find NFT by publicKey
		let nft: NFTWithIncludes | null = await prisma.nFT.findFirst({
			where: {
				publicKey,
			},
			include: {
				owner: true,
				collection: {
					include: {
						owner: true,
					},
				},
			},
		})

		if (!nft) {
			console.log(
				'NFT not found in database, fetching from blockchain...TELEFUNC',
			)
			const asset = await fetchAsset(getServerUmi(), publicKey, {
				skipDerivePlugins: false,
			})

			if (asset) {
				const data = await enhanceAssetWithMetadata(asset)
				if (data?.updateAuthority.address) {
					// collection already exists
					try {
						const collection = await prisma.collection.findFirst({
							where: {
								publicKey: data.updateAuthority.address,
							},
						})

						if (!collection) {
							const coreCollection = await fetchEnhancedCollection(
								data.updateAuthority.address,
							)

							if (coreCollection) {
								const updateAuthority =
									coreCollection.updateAuthority.toString()

								// Find or create user
								let user = await prisma.user.findFirst({
									where: {
										publicKey: updateAuthority,
									},
								})

								if (!user) {
									// Create user if not exists
									user = await prisma.user.create({
										data: {
											publicKey: updateAuthority,
											username: `user_${updateAuthority.slice(0, 8)}`,
										},
									})
								}

								// Create collection
								await prisma.collection.create({
									data: {
										name: coreCollection.name,
										publicKey: updateAuthority,
										bannerUrl: '',
										symbol: coreCollection.metadata.symbol || 'NFT',
										description: coreCollection.metadata.description || '',
										logoUrl: coreCollection.metadata.image || '',
										metadataUri: coreCollection.uri,
										ownerId: user.id, // Use the user ID, not the public key
									},
								})
							}
						}
					} catch (error) {
						console.error('Error processing collection:', error)
					}
				}
				// Find or create owner user
				let owner = await prisma.user.findFirst({
					where: {
						publicKey: asset.owner.toString(),
					},
				})

				if (!owner) {
					// Create owner if not exists
					owner = await prisma.user.create({
						data: {
							publicKey: asset.owner.toString(),
							username: `user_${asset.owner.toString().slice(0, 8)}`,
						},
					})
				}

				// Prepare base NFT data
				const nftData = {
					name: asset.name,
					publicKey,
					description: data?.metadata.description || '',
					imageUrl: data?.metadata.image || '',
					metadataUrl: asset.uri,
					attributes: asset.attributes,
					owner: {
						connect: { id: owner.id },
					},
					creator: {
						connect: { id: owner.id },
					},
				}

				// Find collection if it exists
				let collectionId = undefined
				if (data?.updateAuthority.address) {
					const collection = await prisma.collection.findFirst({
						where: {
							publicKey: data.updateAuthority.address,
						},
					})

					if (collection) {
						collectionId = collection.id
					}
				}

				// Add collection connection if available
				const createData = collectionId
					? {
							...nftData,
							collection: {
								connect: { id: collectionId },
							},
						}
					: nftData

				// Create the NFT
				await prisma.nFT.create({
					data: createData,
				})

				// Fetch the newly created NFT with all the includes we need
				nft = await prisma.nFT.findFirst({
					where: {
						publicKey,
					},
					include: {
						owner: true,
						collection: {
							include: {
								owner: true,
							},
						},
					},
				})
			}
		}

		// If NFT is not found, return null
		if (!nft) {
			return null
		}

		// Generate listing address
		const [listing] = PublicKey.findProgramAddressSync(
			[
				Buffer.from(LISTING_SEED),
				new PublicKey(nft.publicKey).toBuffer(),
				new PublicKey(nft.owner?.publicKey as string).toBuffer(),
			],
			new PublicKey(MARKETPLACE_PROGRAM_ID),
		)

		// Fetch metadata from URL if available
		let attributes = undefined
		if (nft.metadataUrl) {
			try {
				const metadata = await fetchMetadata(nft.metadataUrl)
				if (metadata?.attributes) {
					// Process attributes from metadata
					attributes = metadata.attributes.map((attr) => ({
						id: `${attr.traitType}-${attr.value}`
							.replace(/\s+/g, '-')
							.toLowerCase(),
						traitType: attr.traitType,
						value: attr.value,
					}))
				}
			} catch (error) {
				console.error(
					`Error fetching metadata for NFT ${nft.publicKey}:`,
					error,
				)
			}
		}

		// Fallback to attributes stored in the database if metadata fetch failed
		if (!attributes && nft.attributes) {
			attributes = nft.attributes as Array<{
				id: string
				traitType: string
				value: string
			}>
		}

		// Map to the expected NFT type
		return {
			name: nft.name,
			publicKey: nft.publicKey,
			description: nft.description || '',
			logoUrl: nft.imageUrl,
			symbol: nft.name.substring(0, 3).toUpperCase(),
			totalLikes: nft.totalLikes,
			totalViews: nft.totalViews,
			biddingPrice: nft.lastBiddingPrice || 0,
			owner: {
				id: nft.owner.id,
				username: nft.owner.username,
				imageUrl: nft.owner.imageUrl || '',
				publicKey: nft.owner.publicKey,
			},
			listing: listing.toString(),
			// Additional properties
			collectionId: nft.collectionId || undefined,
			royaltyBasisPoints: nft.royaltyBasisPoints || 0,
			updatedAt: nft.updatedAt?.toISOString(),
			metadataUrl: nft.metadataUrl || undefined,
			isLocked: nft.isLocked || false,
			attributes: attributes,
			// Include collection data if available
			collection: nft.collection
				? {
						name: nft.collection.name,
						description: nft.collection.description,
						logoUrl: nft.collection.logoUrl,
						bannerUrl: nft.collection.bannerUrl,
						publicKey: nft.collection.publicKey,
						owner: nft.collection.owner
							? {
									id: nft.collection.owner.id,
									username: nft.collection.owner.username,
									imageUrl: nft.collection.owner.imageUrl || '',
									publicKey: nft.collection.owner.publicKey,
								}
							: undefined,
					}
				: undefined,
		}
	} catch (error) {
		console.error('Error fetching NFT:', error)
		return null
	}
}

/**
 * Get NFT activity logs
 */
export type NFTActivity = {
	id: string
	type: string
	price?: {
		sol?: number
		usd?: number
	}
	from?: {
		id: string
		username: string
		publicKey: string
	}
	to?: {
		id: string
		username: string
		publicKey: string
	}
	date: string
	transactionHash: string
}

export async function onGetNFTActivities(
	publicKey: string,
): Promise<NFTActivity[]> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to onGetNFTActivities')
			return []
		}

		// First get the NFT ID from the public key
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { id: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found for activity logs`)
			return []
		}

		// Get activity logs for this NFT
		const activityLogs = await prisma.activityLog.findMany({
			where: { nftId: nft.id },
			include: {
				user: true,
			},
			orderBy: { createdAt: 'desc' },
		})

		// Transform activity logs to the expected format
		return activityLogs.map((log) => {
			// Extract price from data if available
			const price =
				log.data && typeof log.data === 'object' && 'amount' in log.data
					? {
							sol:
								Number(
									(log.data as Record<string, unknown>).amount as string,
								) || 0,
						}
					: undefined

			// Extract from/to users if available in data
			const from = log.user
				? {
						id: log.user.id,
						username: log.user.username,
						publicKey: log.user.publicKey,
					}
				: undefined

			// For transfers, the 'to' user might be in the data
			const to =
				log.data && typeof log.data === 'object' && 'buyerId' in log.data
					? {
							id: (log.data as Record<string, unknown>).buyerId as string,
							username: 'User',
							publicKey: '',
						}
					: undefined

			return {
				id: log.id,
				type: log.type,
				price,
				from,
				to,
				date: log.createdAt.toISOString(),
				transactionHash: log.transactionHash,
			}
		})
	} catch (error) {
		console.error('Error fetching NFT activities:', error)
		return []
	}
}

/**
 * Get related NFTs from the same collection
 */
export async function onGetRelatedNFTs(
	publicKey: string,
	limit = 10,
): Promise<NFT[]> {
	try {
		if (!publicKey) {
			console.error('Invalid publicKey provided to onGetRelatedNFTs')
			return []
		}

		// First get the NFT to find its collection
		const nft = await prisma.nFT.findFirst({
			where: { publicKey },
			select: { collectionId: true },
		})

		if (!nft) {
			console.log(`NFT with publicKey ${publicKey} not found`)
			return []
		}

		if (!nft.collectionId) {
			console.log(`NFT with publicKey ${publicKey} has no collection`)
			return []
		}

		// Get other NFTs from the same collection
		const relatedNfts = await prisma.nFT.findMany({
			where: {
				collectionId: nft.collectionId || undefined,
				NOT: { publicKey }, // Exclude the current NFT
			},
			select: {
				id: true,
				name: true,
				publicKey: true,
				description: true,
				imageUrl: true,
				metadataUrl: true,
				attributes: true,
				lastBiddingPrice: true,
				owner: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
			},
			take: limit,
		})

		// Map to the expected NFT type
		const enhancedNfts = []

		// Process each related NFT and fetch metadata if available
		for (const relatedNft of relatedNfts) {
			const [listing] = PublicKey.findProgramAddressSync(
				[
					Buffer.from(LISTING_SEED),
					new PublicKey(relatedNft.publicKey).toBuffer(),
					new PublicKey(relatedNft.owner.publicKey).toBuffer(),
				],
				new PublicKey(MARKETPLACE_PROGRAM_ID),
			)

			// Fetch metadata from URL if available
			let attributes = undefined
			if (relatedNft.metadataUrl) {
				try {
					const metadata = await fetchMetadata(relatedNft.metadataUrl)
					if (metadata?.attributes) {
						// Process attributes from metadata
						attributes = metadata.attributes.map((attr) => ({
							id: `${attr.traitType}-${attr.value}`
								.replace(/\s+/g, '-')
								.toLowerCase(),
							traitType: attr.traitType,
							value: attr.value,
						}))
					}
				} catch (error) {
					console.error(
						`Error fetching metadata for NFT ${relatedNft.publicKey}:`,
						error,
					)
				}
			}

			// Fallback to attributes stored in the database if metadata fetch failed
			if (!attributes && relatedNft.attributes) {
				attributes = relatedNft.attributes as Array<{
					id: string
					traitType: string
					value: string
				}>
			}

			enhancedNfts.push({
				name: relatedNft.name,
				publicKey: relatedNft.publicKey,
				description: relatedNft.description || '',
				logoUrl: relatedNft.imageUrl,
				symbol: relatedNft.name.substring(0, 3).toUpperCase(),
				biddingPrice: relatedNft.lastBiddingPrice || 0,
				owner: {
					id: relatedNft.owner.id,
					username: relatedNft.owner.username,
					imageUrl: relatedNft.owner.imageUrl || '',
					publicKey: relatedNft.owner.publicKey,
				},
				listing: listing.toString(),
				attributes,
			})
		}

		return enhancedNfts
	} catch (error) {
		console.error('Error fetching related NFTs:', error)
		return []
	}
}

// export async function onRecordActivityLog(
// 	nftId: string,
// 	activityType: string,
// 	data: Record<string, unknown>,
// 	userId: string,
// 	transactionHash: string,
// )
